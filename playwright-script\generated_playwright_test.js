const path = require('path'); 
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const { chromium } = require('playwright');
const fs = require('fs');
const OpenAI = require('openai');

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

const stepRetryCount = {};

async function analyzeWithOpenAI(error, domContent, stepName) {
    try {
        const response = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
                { role: "system", content: "Analyze if this error is caused by a popup. Return only CSS selector to close popup or 'NO_POPUP'." },
                { role: "user", content: `Error: ${error}\nDOM: ${domContent.substring(0, 1000)}` }
            ],
            max_tokens: 100
        });
        return response.choices[0].message.content.trim();
    } catch (err) {
        console.error(`OpenAI analyzeWithOpenAI error: ${err.message}`);
        return 'NO_POPUP';
    }
}

async function captureFailureEvidence(page, stepName) {
    try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const screenshotPath = `failure_${stepName}_${timestamp}.png`;
        const domPath = `failure_${stepName}_${timestamp}.html`;
        await page.screenshot({ path: screenshotPath, fullPage: true });
        const domContent = await page.content();
        fs.writeFileSync(domPath, domContent, 'utf-8');
        return { screenshotPath, domPath, domContent };
    } catch (err) {
        console.error(`Failed to capture failure evidence for step ${stepName}: ${err.message}`);
        return { screenshotPath: null, domPath: null, domContent: '' };
    }
}

async function performStepWithRetries(stepName, stepFunction, page) {
    while (true) {
        try {
            await stepFunction();
            stepRetryCount[stepName] = 0;
            return;
        } catch (err) {
            stepRetryCount[stepName] = (stepRetryCount[stepName] || 0) + 1;
            console.warn(`Step "${stepName}" failed attempt ${stepRetryCount[stepName]}: ${err.message}`);

            if (stepRetryCount[stepName] >= 3) {
                const { screenshotPath, domPath, domContent } = await captureFailureEvidence(page, stepName);
                const popupSelector = await analyzeWithOpenAI(err.message, domContent, stepName);

                if (popupSelector !== 'NO_POPUP' && popupSelector) {
                    try {
                        console.log(`Popup detected by AI for step "${stepName}". Attempting to close popup using selector: ${popupSelector}`);
                        await page.click(popupSelector, { timeout: 3000 });
                        // Reset retry count and retry step after closing popup
                        stepRetryCount[stepName] = 0;
                        // Wait shortly for UI to stabilize after popup close
                        await page.waitForTimeout(1500);
                        continue;
                    } catch (popupErr) {
                        console.error(`Failed to close popup with selector "${popupSelector}": ${popupErr.message}`);
                    }
                }

                console.error(`Step "${stepName}" failed after 3 attempts. Screenshot: ${screenshotPath || 'N/A'}, DOM saved at: ${domPath || 'N/A'}`);
                process.exit(1);
            }

            // Wait before retrying
            await page.waitForTimeout(1000);
        }
    }
}

(async () => {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();

    // Helper function to try multiple selectors with fallback
    async function clickWithFallback(selectors, stepName) {
        for (const selector of selectors) {
            try {
                await page.waitForSelector(selector, { timeout: 4000, state: 'visible' });
                await page.click(selector, { timeout: 4000 });
                return;
            } catch {}
        }
        throw new Error(`None of the selectors matched or clickable for step "${stepName}": ${selectors.join(', ')}`);
    }

    async function fillWithFallback(selectors, text, stepName) {
        for (const selector of selectors) {
            try {
                await page.waitForSelector(selector, { timeout: 4000, state: 'visible' });
                await page.fill(selector, text, { timeout: 4000 });
                return;
            } catch {}
        }
        throw new Error(`None of the selectors matched or fillable for step "${stepName}": ${selectors.join(', ')}`);
    }

    try {
        // Step 1: Navigate to https://dashboard.mindler.com
        await performStepWithRetries('Navigate to dashboard.mindler.com', async () => {
            const response = await page.goto('https://dashboard.mindler.com', { timeout: 5000, waitUntil: 'domcontentloaded' });
            if (!response || !response.ok()) {
                throw new Error(`Navigation failed with status ${response ? response.status() : 'no response'}`);
            }
            // Validate URL contains expected domain
            if (!page.url().includes('dashboard.mindler.com')) {
                throw new Error(`Unexpected URL after navigation: ${page.url()}`);
            }
        }, page);

        // Step 2: Wait for login page to load (URL contains /login)
        await performStepWithRetries('Wait for login page', async () => {
            await page.waitForURL('**/login', { timeout: 5000 });
            // Validate presence of email input field
            const emailSelectors = [
                'input[name="email"]',
                'input[placeholder*="Email"]',
                'input[type="text"][name="email"]',
                'input[type="text"][placeholder*="email"]'
            ];
            let found = false;
            for (const sel of emailSelectors) {
                if (await page.$(sel)) {
                    found = true;
                    break;
                }
            }
            if (!found) throw new Error('Email input field not found on login page');
        }, page);

        // Step 3: Input email '<EMAIL>'
        await performStepWithRetries('Input email', async () => {
            const emailSelectors = [
                'input[name="email"]',
                'input[placeholder*="Email"]',
                'input[type="text"][name="email"]',
                'input[type="text"][placeholder*="email"]'
            ];
            await fillWithFallback(emailSelectors, '<EMAIL>', 'Input email');
        }, page);

        // Step 4: Input password '12345'
        await performStepWithRetries('Input password', async () => {
            const passwordSelectors = [
                'input[name="password"]',
                'input[placeholder*="Password"]',
                'input[type="password"][name="password"]',
                'input[type="password"][placeholder*="password"]'
            ];
            await fillWithFallback(passwordSelectors, '12345', 'Input password');
        }, page);

        // Step 5: Click Continue button
        await performStepWithRetries('Click Continue button', async () => {
            const continueSelectors = [
                'form button.cbtn.cbtn--text',
                'form button[type="submit"]',
                'button:has-text("Continue")',
                'button:has-text("continue")'
            ];
            await clickWithFallback(continueSelectors, 'Click Continue button');
            // Wait for navigation after login
            await page.waitForNavigation({ timeout: 5000, waitUntil: 'domcontentloaded' });
            if (!page.url().includes('/dashboard')) {
                throw new Error(`Did not navigate to dashboard after login, current URL: ${page.url()}`);
            }
        }, page);

        // Step 6: Handle possible Orion AI popup overlay by closing it if present
        // The popup close button was at index 2 with css_selector:
        // "html > body > div:nth-of-type(2) > div:nth-of-type(3) > div:nth-of-type(2) > div:nth-of-type(2) > div"
        // We'll try multiple selectors for popup close button
        await performStepWithRetries('Close Orion AI popup if present', async () => {
            const popupCloseSelectors = [
                'div[aria-label="Close"]',
                'button[aria-label="Close"]',
                'div[class*="popup"] button.close',
                'div:nth-of-type(2) > div:nth-of-type(3) > div:nth-of-type(2) > div:nth-of-type(2) > div',
                'div[role="dialog"] button[aria-label="Close"]',
                'button:has-text("×")',
                'button:has-text("Close")',
                'button:has-text("close")'
            ];
            // Try to find any visible popup close button and click it
            let clicked = false;
            for (const sel of popupCloseSelectors) {
                const el = await page.$(sel);
                if (el) {
                    const visible = await el.isVisible();
                    if (visible) {
                        await el.click({ timeout: 3000 });
                        clicked = true;
                        // Wait shortly for popup to close
                        await page.waitForTimeout(1500);
                        break;
                    }
                }
            }
            // If no popup found, just continue
        }, page);

        // Step 7: Click the dropdown button next to profile icon (index 6)
        // CSS selector from JSON: 
        // "html > body > div:nth-of-type(1) > div > div:nth-of-type(1) > div:nth-of-type(1) > div > div > div:nth-of-type(2) > div:nth-of-type(3) > div > button[type=\"button\"]"
        // We'll try robust selectors for profile dropdown button
        await performStepWithRetries('Click profile dropdown button', async () => {
            const dropdownSelectors = [
                'button[aria-haspopup="true"]',
                'button[type="button"].profile-dropdown, button[type="button"][aria-label*="profile"], button[type="button"][aria-expanded]',
                'div[role="navigation"] button[type="button"]',
                'button:has-text("▼")',
                'button:has-text("▾")',
                'button:has-text("Profile")',
                'button[type="button"]'
            ];
            await clickWithFallback(dropdownSelectors, 'Click profile dropdown button');
            // Wait for dropdown menu to appear
            const dropdownMenuSelectors = [
                'div[role="menu"]',
                'ul[role="menu"]',
                'div[aria-label="profile menu"]',
                'div[role="listbox"]'
            ];
            let foundMenu = false;
            for (const sel of dropdownMenuSelectors) {
                try {
                    await page.waitForSelector(sel, { timeout: 3000, state: 'visible' });
                    foundMenu = true;
                    break;
                } catch {}
            }
            if (!foundMenu) {
                // Not necessarily fatal, but warn
                console.warn('Profile dropdown menu did not appear after clicking dropdown button');
            }
        }, page);

        // Step 8: Click the 'Log Out' option at index 9
        // From JSON, the logout option is a div inside dropdown menu
        // We'll try to find menu item with text 'Log Out' or 'Logout'
        await performStepWithRetries('Click Log Out option', async () => {
            const logoutSelectors = [
                'text=Log Out',
                'text=Logout',
                'div[role="menuitem"]:has-text("Log Out")',
                'div[role="menuitem"]:has-text("Logout")',
                'button:has-text("Log Out")',
                'button:has-text("Logout")',
                'li:has-text("Log Out")',
                'li:has-text("Logout")'
            ];
            let clicked = false;
            for (const sel of logoutSelectors) {
                const el = await page.$(sel);
                if (el) {
                    const visible = await el.isVisible();
                    if (visible) {
                        await el.click({ timeout: 4000 });
                        clicked = true;
                        break;
                    }
                }
            }
            if (!clicked) {
                throw new Error('Could not find or click Log Out option in dropdown menu');
            }
            // Wait for navigation back to login page
            await page.waitForURL('**/login', { timeout: 5000 });
            if (!page.url().includes('/login')) {
                throw new Error(`Did not navigate back to login page after logout, current URL: ${page.url()}`);
            }
        }, page);

        console.log('Test completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();
    }
})();